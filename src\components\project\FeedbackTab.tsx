
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { getStatusBadge } from "@/utils/statusBadge";
import { formatDate } from "@/utils/formatDate";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface FeedbackItem {
  email: string;
  feedback: string;
  date: number;
  status: string;
}

interface FeedbackTabProps {
  feedback: FeedbackItem[];
  onUpdateStatus?: (index: number, status: string) => void;
}

const FeedbackTab: React.FC<FeedbackTabProps> = ({ feedback, onUpdateStatus }) => {
  return (
    <div className="space-y-4">
      {feedback.length > 0 ? (
        feedback.map((item, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <span>{item.email}</span>
                  {getStatusBadge(item.status)}
                </div>
                <span className="text-sm font-normal text-muted-foreground">
                  {formatDate(item.date)}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>{item.feedback}</p>
              
              {onUpdateStatus && (
                <div className="flex justify-end mt-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">Update Status</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "unread")}>
                        Unread
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "read")}>
                        Read
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onUpdateStatus(index, "completed")}>
                        Completed
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </CardContent>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No feedback received yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FeedbackTab;
