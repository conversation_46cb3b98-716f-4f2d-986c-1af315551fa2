import React from "react";
import { motion } from "framer-motion";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import Hero from "@/components/sections/QuickDocsHero";
import Features from "@/components/sections/QuickDocsFeatures";
import TemplateDemo from "@/components/sections/TemplateDemo";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import { ThemeToggle } from "@/components/ThemeToggle";

const Index = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="relative min-h-screen bg-linear-to-b from-background to-secondary/20 dark:from-background dark:to-secondary/5"
    >
      <div className="fixed top-4 right-4 z-50">
        <ThemeToggle />
      </div>
      
      <Header />
      
      <main>
        <Hero />
        <Features />
        <TemplateDemo />
        
        <section id="get-started" className="py-20 px-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-xl p-8 shadow-lg border border-border">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div>
                  <h2 className="text-2xl md:text-3xl font-medium mb-4">Ready to streamline your document workflow?</h2>
                  <p className="text-muted-foreground mb-6">
                    QuickDocs helps you create, manage, and fill document templates with ease.
                    Say goodbye to manual document formatting and hello to automation.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button asChild size="lg" className="gap-2">
                      <Link to="/dashboard">
                        Try QuickDocs Now <ArrowRight size={16} />
                      </Link>
                    </Button>
                    <Button variant="outline" size="lg">
                      See pricing
                    </Button>
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-primary/5 rounded-lg p-6 relative">
                    <ul className="space-y-3">
                      <li className="flex items-center gap-3 text-sm">
                        <span className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">1</span>
                        <span>Create a document structure</span>
                      </li>
                      <li className="flex items-center gap-3 text-sm">
                        <span className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">2</span>
                        <span>Upload your DOCX template</span>
                      </li>
                      <li className="flex items-center gap-3 text-sm">
                        <span className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">3</span>
                        <span>Fill in the form fields</span>
                      </li>
                      <li className="flex items-center gap-3 text-sm">
                        <span className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary">4</span>
                        <span>Generate your document</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </motion.div>
  );
};

export default Index;
