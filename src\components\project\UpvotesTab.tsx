
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { formatDate } from "@/utils/formatDate";
import { ThumbsUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface UpvoteItem {
  id: number;
  feature: string;
  detail: string;
  upvotes: number;
  date: number;
}

interface UpvotesTabProps {
  upvotes: UpvoteItem[];
  onVote?: (id: number) => void;
}

const UpvotesTab: React.FC<UpvotesTabProps> = ({ upvotes, onVote }) => {
  return (
    <div className="space-y-4">
      {upvotes.length > 0 ? (
        upvotes.map((item) => (
          <Card key={item.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <span>{item.feature}</span>
                  <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full flex items-center gap-1">
                    <ThumbsUp className="h-3 w-3" /> {item.upvotes}
                  </span>
                </div>
                <span className="text-sm font-normal text-muted-foreground">
                  {formatDate(item.date)}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{item.detail}</p>
              
              {onVote && (
                <div className="flex justify-end mt-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => onVote(item.id)}
                    className="flex items-center gap-1"
                  >
                    <ThumbsUp className="h-4 w-4" /> Vote
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No features to vote on yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UpvotesTab;
