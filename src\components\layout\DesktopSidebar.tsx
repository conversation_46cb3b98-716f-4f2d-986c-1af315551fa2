
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Home, 
  Settings, 
  LogOut, 
  PlusCircle, 
  BarChart,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";

type DesktopSidebarProps = {
  collapsed: boolean;
  toggleSidebar: () => void;
  handleLogout: () => void;
};

const DesktopSidebar = ({ collapsed, toggleSidebar, handleLogout }: DesktopSidebarProps) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div 
      className={`hidden md:flex h-screen flex-col bg-card border-r border-border transition-all duration-300 ${
        collapsed ? "w-16" : "w-64"
      }`}
    >
      <div className="p-4 border-b border-border flex items-center justify-between">
        <Link to="/dashboard" className="flex items-center">
          <FileText className="h-6 w-6 mr-2 text-primary" />
          {!collapsed && (
            <span className="text-xl font-medium text-foreground">
              Feed<span className="font-bold">Map</span>
            </span>
          )}
        </Link>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={toggleSidebar}
          className="text-muted-foreground hover:text-foreground"
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </Button>
      </div>
      <nav className="flex-1 overflow-y-auto py-4">
        <div className="px-4 mb-2">
          <Button 
            asChild 
            className={`w-full justify-start ${collapsed ? "px-0" : ""}`}
            variant="default"
          >
            <Link to="/projects/new">
              <PlusCircle className="h-4 w-4 mx-auto md:mr-2" />
              {!collapsed && <span>New Project</span>}
            </Link>
          </Button>
        </div>
        <div className="px-2 mt-4">
          <div className="space-y-1">
            <Link
              to="/dashboard"
              className={`flex items-center px-3 py-2 text-sm rounded-md ${
                isActive("/dashboard")
                  ? "bg-primary/10 text-primary font-medium"
                  : "text-muted-foreground hover:bg-accent"
              } ${collapsed ? "justify-center" : ""}`}
            >
              <Home className="h-4 w-4 mx-auto md:mr-2" />
              {!collapsed && <span>Dashboard</span>}
            </Link>
            <Link
              to="/analytics"
              className={`flex items-center px-3 py-2 text-sm rounded-md ${
                isActive("/analytics")
                  ? "bg-primary/10 text-primary font-medium"
                  : "text-muted-foreground hover:bg-accent"
              } ${collapsed ? "justify-center" : ""}`}
            >
              <BarChart className="h-4 w-4 mx-auto md:mr-2" />
              {!collapsed && <span>Analytics</span>}
            </Link>
            <Link
              to="/settings"
              className={`flex items-center px-3 py-2 text-sm rounded-md ${
                isActive("/settings")
                  ? "bg-primary/10 text-primary font-medium"
                  : "text-muted-foreground hover:bg-accent"
              } ${collapsed ? "justify-center" : ""}`}
            >
              <Settings className="h-4 w-4 mx-auto md:mr-2" />
              {!collapsed && <span>Settings</span>}
            </Link>
          </div>
        </div>
      </nav>
      <div className={`p-4 border-t border-border flex ${collapsed ? "justify-center" : "justify-between"} items-center`}>
        {!collapsed && (
          <button
            onClick={handleLogout}
            className="flex items-center text-sm text-muted-foreground hover:text-primary"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Log out
          </button>
        )}
        {collapsed ? (
          <button
            onClick={handleLogout}
            className="text-muted-foreground hover:text-primary"
          >
            <LogOut className="h-5 w-5" />
          </button>
        ) : null}
        <ThemeToggle />
      </div>
    </div>
  );
};

export default DesktopSidebar;
