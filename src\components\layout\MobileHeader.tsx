
import React from "react";
import { Link } from "react-router-dom";
import { FileText } from "lucide-react";
import MobileSidebar from "./MobileSidebar";

type MobileHeaderProps = {
  handleLogout: () => void;
};

const MobileHeader = ({ handleLogout }: MobileHeaderProps) => {
  return (
    <div className="md:hidden p-4 border-b border-border flex justify-between items-center">
      <MobileSidebar handleLogout={handleLogout} />
      <Link to="/dashboard" className="flex items-center">
        <FileText className="h-5 w-5 mr-2 text-primary" />
        <span className="text-lg font-medium text-foreground">
          Feed<span className="font-bold">Map</span>
        </span>
      </Link>
      <div className="w-10"></div> {/* Spacer for alignment */}
    </div>
  );
};

export default MobileHeader;
