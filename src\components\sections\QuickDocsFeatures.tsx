
import React from "react";
import { motion } from "framer-motion";
import FeatureCard from "@/components/ui/FeatureCard";
import { FileText, FolderTree, Upload, Lock, Cloud, Zap, Users, LayoutGrid } from "lucide-react";

const features = [
  {
    title: "File Tree Organization",
    description: "Organize templates with intuitive folder structures for easy navigation and management.",
    icon: <FolderTree className="h-6 w-6" />
  },
  {
    title: "Template Processing",
    description: "Upload DOCX templates and automatically identify templating keys and form fields.",
    icon: <FileText className="h-6 w-6" />
  },
  {
    title: "Smart Form Layout",
    description: "View and fill template forms with flexible layouts - vertical, tabbed, or side-by-side.",
    icon: <LayoutGrid className="h-6 w-6" />
  },
  {
    title: "Cloud Storage",
    description: "All your templates and documents are securely stored in the cloud for access anywhere.",
    icon: <Cloud className="h-6 w-6" />
  },
  {
    title: "Secure Authentication",
    description: "Keep your sensitive documents protected with robust login and authentication.",
    icon: <Lock className="h-6 w-6" />
  },
  {
    title: "Instant Generation",
    description: "Generate perfectly formatted documents in seconds with your form data.",
    icon: <Zap className="h-6 w-6" />
  },
  {
    title: "Team Collaboration",
    description: "Share templates and collaborate with team members on document creation.",
    icon: <Users className="h-6 w-6" />
  },
  {
    title: "Bulk Processing",
    description: "Generate multiple documents at once using data imports for maximum efficiency.",
    icon: <Upload className="h-6 w-6" />
  }
];

const QuickDocsFeatures = () => {
  return (
    <section id="features" className="py-20 px-6 bg-secondary/20">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <span className="text-xs font-medium bg-primary/10 text-primary rounded-full px-3 py-1">
            Features
          </span>
          <h2 className="mt-6 text-3xl md:text-4xl font-medium tracking-tight">
            Everything You Need for Document Automation
          </h2>
          <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
            QuickDocs combines powerful template processing with intuitive organization tools to 
            streamline your document workflows.
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              index={index}
              className="bg-white"
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default QuickDocsFeatures;
