import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ProjectEnabledFeatures } from "@/types/project";

interface SettingsTabProps {
  formData: {
    name: string;
    domain: string;
    greeting: string;
    contact: {
      address: string;
      phone: string;
      email: string;
    };
    enabled: ProjectEnabledFeatures;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleContactChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleToggleChange: (feature: keyof ProjectEnabledFeatures) => void;
  handleSaveSettings: () => void;
}

const SettingsTab: React.FC<SettingsTabProps> = ({ 
  formData, 
  handleInputChange, 
  handleContactChange, 
  handleToggleChange,
  handleSaveSettings
}) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Settings</CardTitle>
          <CardDescription>Update your project configuration</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Project Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="domain">Domain</Label>
            <Input
              id="domain"
              name="domain"
              value={formData.domain}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="greeting">Greeting Message</Label>
            <Input
              id="greeting"
              name="greeting"
              value={formData.greeting}
              onChange={handleInputChange}
            />
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Widget Features</CardTitle>
          <CardDescription>Enable or disable feedback collection methods</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Feedback</p>
              <p className="text-sm text-muted-foreground">
                Allow users to submit general feedback
              </p>
            </div>
            <Switch
              checked={formData.enabled.feedback}
              onCheckedChange={() => handleToggleChange("feedback")}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Bug Reports</p>
              <p className="text-sm text-muted-foreground">
                Allow users to report bugs
              </p>
            </div>
            <Switch
              checked={formData.enabled.bug}
              onCheckedChange={() => handleToggleChange("bug")}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Feature Requests</p>
              <p className="text-sm text-muted-foreground">
                Allow users to request new features
              </p>
            </div>
            <Switch
              checked={formData.enabled.feature}
              onCheckedChange={() => handleToggleChange("feature")}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">FAQ</p>
              <p className="text-sm text-muted-foreground">
                Show FAQ section in widget
              </p>
            </div>
            <Switch
              checked={formData.enabled.faq}
              onCheckedChange={() => handleToggleChange("faq")}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Contact Information</p>
              <p className="text-sm text-muted-foreground">
                Display contact details
              </p>
            </div>
            <Switch
              checked={formData.enabled.contact}
              onCheckedChange={() => handleToggleChange("contact")}
            />
          </div>
        </CardContent>
      </Card>
      
      {formData.enabled.contact && (
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Update contact details shown in the widget</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contact-email">Email</Label>
              <Input
                id="contact-email"
                name="email"
                value={formData.contact.email}
                onChange={handleContactChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contact-phone">Phone</Label>
              <Input
                id="contact-phone"
                name="phone"
                value={formData.contact.phone}
                onChange={handleContactChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="contact-address">Address</Label>
              <Textarea
                id="contact-address"
                name="address"
                rows={3}
                value={formData.contact.address}
                onChange={handleContactChange}
              />
            </div>
          </CardContent>
        </Card>
      )}
      
      <div>
        <Button onClick={handleSaveSettings}>Save Settings</Button>
      </div>
    </div>
  );
};

export default SettingsTab;
