import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import AppLayout from "@/components/layout/AppLayout";
import ChatbotPreview from "@/components/ChatbotPreview";
import WidgetFeaturesCard from "@/components/project/WidgetFeaturesCard";
import ContactInfoCard from "@/components/project/ContactInfoCard";
import FaqItemsCard from "@/components/project/FaqItemsCard";

interface FormData {
  name: string;
  domain: string;
  greeting: string;
  enabled: {
    feedback: boolean;
    bug: boolean;
    feature: boolean;
    faq: boolean;
    contact: boolean;
  };
  contact: {
    address: string;
    phone: string;
    email: string;
  };
  faq: Array<{
    id: number;
    question: string;
    answer: string;
  }>;
}

const NewProject = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<FormData>({
    name: "",
    domain: "",
    greeting: "Hey 👋",
    enabled: {
      feedback: true,
      bug: true,
      feature: true,
      faq: false,
      contact: false
    },
    contact: {
      address: "",
      phone: "",
      email: ""
    },
    faq: []
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleToggleChange = (feature: keyof typeof formData.enabled) => {
    setFormData((prev) => ({
      ...prev,
      enabled: {
        ...prev.enabled,
        [feature]: !prev.enabled[feature],
      },
    }));
  };

  const handleContactChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      contact: {
        ...prev.contact,
        [name]: value,
      },
    }));
  };

  const handleFaqChange = (
    id: number,
    field: "question" | "answer",
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      faq: prev.faq.map((faq) =>
        faq.id === id ? { ...faq, [field]: value } : faq
      ),
    }));
  };

  const addFaqItem = () => {
    const newId = formData.faq.length > 0 
      ? Math.max(...formData.faq.map((faq) => faq.id)) + 1 
      : 0;
      
    setFormData((prev) => ({
      ...prev,
      faq: [...prev.faq, { id: newId, question: "", answer: "" }],
    }));
  };

  const removeFaqItem = (id: number) => {
    setFormData((prev) => ({
      ...prev,
      faq: prev.faq.filter((faq) => faq.id !== id),
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    toast({
      title: "Project created",
      description: `${formData.name} has been successfully created.`,
    });
    
    navigate("/dashboard");
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Create New Project</h1>
          <p className="text-muted-foreground mt-1">Configure your FeedMap widget with an improved experience</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-8">
            {/* Basic Information - Full Width */}
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-lg font-semibold mb-4">Basic Information</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium">Project Name</Label>
                        <Input
                          id="name"
                          name="name"
                          placeholder="My Awesome Project"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="transition-all duration-200 focus:ring-2 focus:ring-blue-500/20"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="domain" className="text-sm font-medium">
                          Domain
                        </Label>
                        <Input
                          id="domain"
                          name="domain"
                          placeholder="example.com"
                          value={formData.domain}
                          onChange={handleInputChange}
                          className="transition-all duration-200 focus:ring-2 focus:ring-blue-500/20"
                          required
                        />
                        <p className="text-xs text-muted-foreground">
                          Where the widget will be displayed
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="greeting" className="text-sm font-medium">Greeting Text</Label>
                        <Input
                          id="greeting"
                          name="greeting"
                          placeholder="Hey 👋"
                          value={formData.greeting}
                          onChange={handleInputChange}
                          className="transition-all duration-200 focus:ring-2 focus:ring-blue-500/20"
                        />
                        <p className="text-xs text-muted-foreground">
                          This text will be displayed when the widget is first opened
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Widget Features and Preview - Side by Side */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Configuration Column */}
              <div className="space-y-6">
                <Accordion type="multiple" defaultValue={["features"]} className="space-y-4">
                  {/* Widget Features */}
                  <AccordionItem value="features" className="border rounded-lg">
                    <AccordionTrigger className="px-6 py-4 hover:no-underline">
                      <span className="text-lg font-semibold">Widget Features</span>
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                      <WidgetFeaturesCard
                        enabled={formData.enabled}
                        onToggleChange={handleToggleChange}
                      />
                    </AccordionContent>
                  </AccordionItem>

                  {/* Contact Information */}
                  {formData.enabled.contact && (
                    <AccordionItem value="contact" className="border rounded-lg">
                      <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <span className="text-lg font-semibold">Contact Information</span>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pb-6">
                        <ContactInfoCard
                          contact={formData.contact}
                          onChange={handleContactChange}
                        />
                      </AccordionContent>
                    </AccordionItem>
                  )}

                  {/* FAQ Items */}
                  {formData.enabled.faq && (
                    <AccordionItem value="faq" className="border rounded-lg">
                      <AccordionTrigger className="px-6 py-4 hover:no-underline">
                        <span className="text-lg font-semibold">FAQ Items</span>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 pb-6">
                        <FaqItemsCard
                          faq={formData.faq}
                          onFaqChange={handleFaqChange}
                          onAddFaq={addFaqItem}
                          onRemoveFaq={removeFaqItem}
                        />
                      </AccordionContent>
                    </AccordionItem>
                  )}
                </Accordion>

                <div className="pt-6">
                  <Button type="submit" className="w-full lg:w-auto h-12 px-8 text-base font-medium">
                    Create Project
                  </Button>
                </div>
              </div>

              {/* Preview Column */}
              <div className="lg:sticky lg:top-8 space-y-4">
                <h2 className="text-xl font-bold">Widget Preview</h2>
                <div className="border border-border rounded-lg bg-white p-6 h-[600px] overflow-hidden shadow-md">
                  <div className="h-full bg-slate-100 rounded-lg flex items-center justify-center">
                    <ChatbotPreview
                      project={{
                        greeting: formData.greeting,
                        enabled: formData.enabled,
                        contact: formData.contact,
                        faq: formData.faq
                      }}
                    />
                  </div>
                </div>
                <p className="text-sm text-muted-foreground text-center">
                  This is how your widget will appear to your users
                </p>
              </div>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
};

export default NewProject;
