
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { TabsContent } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import AppLayout from "@/components/layout/AppLayout";
import ProjectHeader from "@/components/project/ProjectHeader";
import ProjectTabs from "@/components/project/ProjectTabs";
import FeedbackTab from "@/components/project/FeedbackTab";
import BugReportsTab from "@/components/project/BugReportsTab";
import FeatureRequestsTab from "@/components/project/FeatureRequestsTab";
import UpvotesTab from "@/components/project/UpvotesTab";
import RoadmapTab from "@/components/project/RoadmapTab";
import SettingsTab from "@/components/project/SettingsTab";
import { Project, FeatureRequest, UpvoteItem } from "@/types/project";

// Mock data for a specific project
const projectData: Project = {
  id: "6dR0EJL642KqjFddl9sI",
  name: "Dev Imob Host",
  domain: "dev.imob.host",
  greeting: "Hey 👋",
  feedback: [
    {
      email: "<EMAIL>",
      feedback: "I love the new dashboard design. It's much more intuitive than the previous version.",
      date: 1747369945018,
      status: "read"
    },
    {
      email: "<EMAIL>",
      feedback: "Could you add dark mode support? My eyes hurt when using the app at night.",
      date: 1747269945018,
      status: "unread"
    },
    {
      email: "<EMAIL>",
      feedback: "The loading times have improved significantly after the latest update.",
      date: 1747169945018,
      status: "completed"
    }
  ],
  bug: [
    {
      bug: "Login page freezes",
      email: "<EMAIL>",
      steps: "1. Go to login page\n2. Enter username and password\n3. Click login button\n4. Page freezes and nothing happens",
      date: 1747369967818,
      status: "pending"
    },
    {
      bug: "Images not loading",
      email: "<EMAIL>",
      steps: "Images don't load on the property listing page. I see broken image icons instead.",
      date: 1747269967818,
      status: "fixed"
    }
  ],
  feature: [
    {
      date: 1747369894581,
      feature: "Advanced filtering",
      detail: "Please add more advanced filtering options to the property search feature. I want to filter by specific amenities.",
      status: "planned"
    },
    {
      feature: "Email notifications",
      detail: "Add ability to receive email notifications when new properties match my saved search criteria.",
      date: 1747369986915,
      status: "in_progress"
    },
    {
      feature: "Mobile app",
      detail: "Would be great to have a mobile app for checking listings on the go.",
      date: 1747269986915,
      status: "completed"
    }
  ],
  contact: {
    address: "123 Main Street, Suite 101, San Francisco, CA 94103",
    phone: "+****************",
    email: "<EMAIL>"
  },
  enabled: {
    feedback: true,
    bug: true,
    feature: true,
    faq: true,
    contact: true
  },
  faq: [
    {
      question: "How do I reset my password?",
      answer: "To reset your password, click on the 'Forgot Password' link on the login page and follow the instructions sent to your email.",
      id: 0
    },
    {
      answer: "Our standard support hours are Monday to Friday, 9am to 5pm Pacific Time.",
      id: 1,
      question: "What are your support hours?"
    }
  ],
  roadmap: [
    {
      id: 1,
      title: "Mobile Application",
      description: "Develop native mobile apps for iOS and Android",
      status: "planned",
      targetDate: "2025-09-01"
    },
    {
      id: 2,
      title: "Advanced Analytics",
      description: "Add more detailed analytics and reporting features",
      status: "in_progress",
      targetDate: "2025-07-15"
    },
    {
      id: 3,
      title: "API Improvements",
      description: "Enhance the API with more endpoints and better documentation",
      status: "completed",
      targetDate: "2025-05-30"
    }
  ],
  upvotes: [
    {
      id: 1,
      feature: "Dark Mode",
      detail: "Add a dark mode theme option for better visibility at night",
      upvotes: 24,
      date: 1747369945018
    },
    {
      id: 2,
      feature: "Export Data",
      detail: "Add ability to export listing data to CSV or Excel format",
      upvotes: 18,
      date: 1747269945018
    }
  ]
};

const ProjectDetails = () => {
  const { projectId } = useParams();
  const { toast } = useToast();
  const [project, setProject] = useState<Project>(projectData);
  const [formData, setFormData] = useState({
    name: project.name,
    domain: project.domain,
    greeting: project.greeting,
    contact: project.contact,
    enabled: project.enabled
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      contact: {
        ...prev.contact,
        [name]: value
      }
    }));
  };

  const handleToggleChange = (feature: keyof typeof formData.enabled) => {
    setFormData(prev => ({
      ...prev,
      enabled: {
        ...prev.enabled,
        [feature]: !prev.enabled[feature]
      }
    }));
  };

  const handleSaveSettings = () => {
    // In a real app, you'd update the project settings here
    setProject({
      ...project,
      ...formData
    });

    toast({
      title: "Settings Updated",
      description: "Your project settings have been saved successfully.",
    });
  };

  // Function to update feedback status
  const updateFeedbackStatus = (index: number, newStatus: string) => {
    const updatedFeedback = [...project.feedback];
    updatedFeedback[index] = {
      ...updatedFeedback[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Feedback status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      feedback: updatedFeedback
    }));
  };

  // Function to update bug report status
  const updateBugStatus = (index: number, newStatus: string) => {
    const updatedBugs = [...project.bug];
    updatedBugs[index] = {
      ...updatedBugs[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Bug report status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      bug: updatedBugs
    }));
  };

  // Function to update a feature status
  const updateFeatureStatus = (index: number, newStatus: string) => {
    const updatedFeatures = [...project.feature];
    updatedFeatures[index] = {
      ...updatedFeatures[index],
      status: newStatus
    };

    toast({
      title: "Status Updated",
      description: `Feature request status updated to "${newStatus}".`,
    });

    setProject(prev => ({
      ...prev,
      feature: updatedFeatures
    }));
  };

  // Function to add feature request to roadmap
  const addToRoadmap = (feature: FeatureRequest) => {
    const newRoadmapItem = {
      id: project.roadmap.length + 1,
      title: feature.feature,
      description: feature.detail,
      status: "planned",
      targetDate: new Date().toISOString().split('T')[0]
    };

    toast({
      title: "Added to Roadmap",
      description: `"${feature.feature}" has been added to the roadmap.`,
    });

    setProject(prev => ({
      ...prev,
      roadmap: [...prev.roadmap, newRoadmapItem]
    }));
  };

  // Function to add feature request to upvotes
  const addToUpvote = (feature: FeatureRequest) => {
    const newUpvoteItem: UpvoteItem = {
      id: project.upvotes.length + 1,
      feature: feature.feature,
      detail: feature.detail,
      upvotes: 0,
      date: Date.now()
    };

    toast({
      title: "Added to Upvotes",
      description: `"${feature.feature}" has been added to upvotes for community voting.`,
    });

    setProject(prev => ({
      ...prev,
      upvotes: [...prev.upvotes, newUpvoteItem]
    }));
  };

  // Function to handle voting
  const handleVote = (id: number) => {
    const updatedUpvotes = project.upvotes.map(item => 
      item.id === id ? { ...item, upvotes: item.upvotes + 1 } : item
    );

    toast({
      title: "Vote Recorded",
      description: "Your vote has been recorded. Thank you!",
    });

    setProject(prev => ({
      ...prev,
      upvotes: updatedUpvotes
    }));
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4">
        <ProjectHeader name={project.name} domain={project.domain} />
        
        <ProjectTabs>
          <TabsContent value="feedback">
            <FeedbackTab 
              feedback={project.feedback} 
              onUpdateStatus={updateFeedbackStatus}
            />
          </TabsContent>
          
          <TabsContent value="bugs">
            <BugReportsTab 
              bugReports={project.bug} 
              onUpdateStatus={updateBugStatus}
            />
          </TabsContent>
          
          <TabsContent value="features">
            <FeatureRequestsTab 
              featureRequests={project.feature}
              onUpdateStatus={updateFeatureStatus}
              onAddToRoadmap={addToRoadmap}
              onAddToUpvote={addToUpvote}
            />
          </TabsContent>
          
          <TabsContent value="upvotes">
            <UpvotesTab 
              upvotes={project.upvotes}
              onVote={handleVote}
            />
          </TabsContent>
          
          <TabsContent value="roadmap">
            <RoadmapTab roadmapItems={project.roadmap} />
          </TabsContent>
          
          <TabsContent value="settings">
            <SettingsTab 
              formData={formData}
              handleInputChange={handleInputChange}
              handleContactChange={handleContactChange}
              handleToggleChange={handleToggleChange}
              handleSaveSettings={handleSaveSettings}
            />
          </TabsContent>
        </ProjectTabs>
      </div>
    </AppLayout>
  );
};

export default ProjectDetails;
