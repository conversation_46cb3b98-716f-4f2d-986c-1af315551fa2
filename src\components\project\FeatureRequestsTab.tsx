
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { getStatusBadge } from "@/utils/statusBadge";
import { formatDate } from "@/utils/formatDate";
import { Edit, ArrowRight, ThumbsUp } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface FeatureRequest {
  feature: string;
  detail: string;
  date: number;
  status: string;
}

interface FeatureRequestsTabProps {
  featureRequests: FeatureRequest[];
  onUpdateStatus: (index: number, status: string) => void;
  onAddToRoadmap: (feature: FeatureRequest) => void;
  onAddToUpvote: (feature: FeatureRequest) => void;
}

const FeatureRequestsTab: React.FC<FeatureRequestsTabProps> = ({ 
  featureRequests, 
  onUpdateStatus, 
  onAdd<PERSON>oRoad<PERSON><PERSON>,
  onAddToUpvote
}) => {
  return (
    <div className="space-y-4">
      {featureRequests.length > 0 ? (
        featureRequests.map((item, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <span>{item.feature}</span>
                  {getStatusBadge(item.status)}
                </div>
                <span className="text-sm font-normal text-muted-foreground">
                  {formatDate(item.date)}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">{item.detail}</p>
              <div className="flex items-center justify-end gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center gap-1"
                >
                  <Edit className="h-3 w-3" /> Edit
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">Status</Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onUpdateStatus(index, "planned")}>
                      Planned
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUpdateStatus(index, "in_progress")}>
                      In Progress
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUpdateStatus(index, "completed")}>
                      Completed
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                <Button 
                  size="sm"
                  variant="outline"
                  onClick={() => onAddToUpvote(item)}
                  className="flex items-center gap-1"
                >
                  <ThumbsUp className="h-3 w-3" /> Add to Upvote
                </Button>
                
                <Button 
                  size="sm"
                  onClick={() => onAddToRoadmap(item)}
                  className="flex items-center gap-1"
                >
                  <ArrowRight className="h-3 w-3" /> Add to Roadmap
                </Button>
              </div>
            </CardContent>
          </Card>
        ))
      ) : (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No feature requests received yet.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FeatureRequestsTab;
