
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Bug, MessageSquare, PlusCircle, BarChart2, Settings as SettingsIcon, ThumbsUp } from "lucide-react";

interface ProjectTabsProps {
  defaultValue?: string;
  children: React.ReactNode;
}

const ProjectTabs: React.FC<ProjectTabsProps> = ({ defaultValue = "feedback", children }) => {
  return (
    <Tabs defaultValue={defaultValue}>
      <TabsList className="mb-6 overflow-x-auto flex w-full sm:w-auto">
        <TabsTrigger value="feedback" className="gap-1">
          <MessageSquare className="h-4 w-4" />
          Feedback
        </TabsTrigger>
        <TabsTrigger value="bugs" className="gap-1">
          <Bug className="h-4 w-4" />
          Bug Reports
        </TabsTrigger>
        <TabsTrigger value="features" className="gap-1">
          <PlusCircle className="h-4 w-4" />
          Feature Requests
        </TabsTrigger>
        <TabsTrigger value="upvotes" className="gap-1">
          <ThumbsUp className="h-4 w-4" />
          Upvotes
        </TabsTrigger>
        <TabsTrigger value="roadmap" className="gap-1">
          <BarChart2 className="h-4 w-4" />
          Roadmap
        </TabsTrigger>
        <TabsTrigger value="settings" className="gap-1">
          <SettingsIcon className="h-4 w-4" />
          Settings
        </TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
};

export default ProjectTabs;
