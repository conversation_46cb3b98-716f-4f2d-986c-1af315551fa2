
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "lucide-react";

interface RoadmapItem {
  id: number;
  title: string;
  description: string;
  status: string;
  targetDate: string;
}

interface RoadmapTabProps {
  roadmapItems: RoadmapItem[];
}

const RoadmapTab: React.FC<RoadmapTabProps> = ({ roadmapItems }) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-end mb-4">
        <Button size="sm">Add Roadmap Item</Button>
      </div>
      
      {roadmapItems.map((item) => (
        <Card key={item.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">{item.title}</CardTitle>
              <div className={`px-3 py-1 rounded-full text-xs font-medium 
                ${item.status === 'planned' ? 'bg-blue-100 text-blue-800' : 
                  item.status === 'in_progress' ? 'bg-amber-100 text-amber-800' : 
                  'bg-green-100 text-green-800'}
              `}>
                {item.status === 'planned' ? 'Planned' : 
                 item.status === 'in_progress' ? 'In Progress' : 
                 'Completed'}
              </div>
            </div>
            <CardDescription className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Target: {item.targetDate}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>{item.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default RoadmapTab;
