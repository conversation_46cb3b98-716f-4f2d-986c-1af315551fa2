
import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface ProjectHeaderProps {
  name: string;
  domain: string;
}

const ProjectHeader: React.FC<ProjectHeaderProps> = ({ name, domain }) => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
      <div>
        <h1 className="text-3xl font-bold">{name}</h1>
        <p className="text-muted-foreground mt-1">{domain}</p>
      </div>
      
      <div className="flex gap-4">
        <Button variant="outline" size="sm" onClick={() => window.history.back()}>
          Back
        </Button>
        <Button asChild>
          <a href={`https://${domain}`} target="_blank" rel="noopener noreferrer">
            Visit Website
          </a>
        </Button>
      </div>
    </div>
  );
};

export default ProjectHeader;
