
import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { FileText, FolderTree, Upload } from "lucide-react";
import { Link } from "react-router-dom";

const QuickDocsHero = () => {
  return (
    <section className="relative pt-32 pb-20 px-6 overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-block"
            >
              <span className="px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                Document Automation
              </span>
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="mt-6 text-4xl md:text-5xl lg:text-6xl font-medium tracking-tight"
            >
              QuickDocs
              <span className="text-primary"> Template </span>
              Engine
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-6 text-xl text-muted-foreground max-w-xl"
            >
              Create, organize, and fill document templates efficiently. 
              Upload DOCX files, manage form fields, and generate documents in seconds.
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="mt-8 flex flex-col sm:flex-row gap-4"
            >
              <Button asChild size="lg" className="gap-2">
                <Link to="/dashboard">
                  Get Started <FileText size={16} />
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                Watch Demo
              </Button>
            </motion.div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, delay: 0.6 }}
            className="relative"
          >
            <div className="relative bg-white rounded-xl overflow-hidden shadow-xl border border-border">
              <div className="bg-primary/5 p-3 border-b border-border">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-destructive/20"></div>
                  <div className="h-3 w-3 rounded-full bg-yellow-500/20"></div>
                  <div className="h-3 w-3 rounded-full bg-green-500/20"></div>
                  <span className="text-xs text-muted-foreground ml-2">QuickDocs.app</span>
                </div>
              </div>
              <div className="p-6">
                <div className="flex border-b border-border pb-4">
                  <div className="w-1/4 border-r border-border pr-4">
                    <div className="flex items-center gap-2 text-primary font-medium text-sm mb-4">
                      <FolderTree size={16} />
                      <span>Templates</span>
                    </div>
                    <div className="space-y-2">
                      <div className="bg-secondary/50 p-2 rounded text-xs">Client Onboarding</div>
                      <div className="text-xs p-2">Legal Documents</div>
                      <div className="text-xs p-2">HR Templates</div>
                      <div className="text-xs p-2">Project Management</div>
                    </div>
                  </div>
                  <div className="w-3/4 pl-4">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center gap-2 text-primary font-medium text-sm">
                        <FileText size={16} />
                        <span>Client Agreement</span>
                      </div>
                      <Button size="sm" variant="outline" className="gap-1 text-xs">
                        <Upload size={12} /> Upload Template
                      </Button>
                    </div>
                    <div className="space-y-3">
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Client Name</label>
                        <div className="h-8 bg-secondary/50 rounded text-xs p-2">Acme Corporation</div>
                      </div>
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Project Scope</label>
                        <div className="h-8 bg-secondary/50 rounded text-xs p-2">Website Redesign</div>
                      </div>
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Contract Value</label>
                        <div className="h-8 bg-secondary/50 rounded text-xs p-2">$25,000</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="pt-4 flex justify-end">
                  <Button size="sm" className="text-xs">Generate Document</Button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default QuickDocsHero;
