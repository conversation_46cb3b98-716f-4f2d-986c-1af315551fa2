import React from "react";
import { Link, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { 
  FileText, 
  Home, 
  Settings, 
  LogOut, 
  PlusCircle, 
  BarChart,
  Menu
} from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

type MobileSidebarProps = {
  handleLogout: () => void;
};

const MobileSidebar = ({ handleLogout }: MobileSidebarProps) => {
  const location = useLocation();
  
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 w-[80%] max-w-[280px] sm:w-[280px]">
        <div className="flex h-screen flex-col bg-card">
          <div className="p-4 border-b border-border">
            <Link to="/dashboard" className="flex items-center">
              <FileText className="h-6 w-6 mr-2 text-primary" />
              <span className="text-xl font-medium text-foreground">
                Feed<span className="font-bold">Map</span>
              </span>
            </Link>
          </div>
          <nav className="flex-1 overflow-y-auto py-4">
            <div className="px-4 mb-2">
              <Button 
                asChild 
                className="w-full justify-start"
                variant="default"
              >
                <Link to="/projects/new">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  New Project
                </Link>
              </Button>
            </div>
            <div className="px-2 mt-4">
              <div className="space-y-1">
                <Link
                  to="/dashboard"
                  className={`flex items-center px-3 py-2 text-sm rounded-md ${
                    isActive("/dashboard")
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-accent"
                  }`}
                >
                  <Home className="h-4 w-4 mr-2" />
                  Dashboard
                </Link>
                <Link
                  to="/analytics"
                  className={`flex items-center px-3 py-2 text-sm rounded-md ${
                    isActive("/analytics")
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-accent"
                  }`}
                >
                  <BarChart className="h-4 w-4 mr-2" />
                  Analytics
                </Link>
                <Link
                  to="/settings"
                  className={`flex items-center px-3 py-2 text-sm rounded-md ${
                    isActive("/settings")
                      ? "bg-primary/10 text-primary font-medium"
                      : "text-muted-foreground hover:bg-accent"
                  }`}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Link>
              </div>
            </div>
          </nav>
          <div className="p-4 border-t border-border flex justify-between items-center">
            <button
              onClick={handleLogout}
              className="flex items-center text-sm text-muted-foreground hover:text-primary"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Log out
            </button>
            <ThemeToggle />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileSidebar;
